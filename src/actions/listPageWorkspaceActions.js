import * as actionTypes from './actionTypes';

// Resource workspace actions
export function digestSelectResourceWorkspace(workspaceGuid) {
    return {
        type: actionTypes.DIGEST_SELECT_RESOURCE_WORKSPACE,
        payload: {
            workspaceGuid
        }
    }
}

export function selectResourceWorkspace(workspaceGuid) {
    return {
        type: actionTypes.SELECT_RESOURCE_WORKSPACE,
        payload: {
            workspaceGuid
        }
    }
}

export function loadResourceWorkspace(workspaceGuid, workspaceChangeUUID, selectWorkspace) {
    return {
        type: actionTypes.LOAD_RESOURCE_WORKSPACE,
        payload: {
            workspaceGuid,
            selectWorkspace,
            workspaceChangeUUID
        }
    }
}

export function loadResourceWorkspaceSuccess(alias, payload, data) {
    return {
        type: actionTypes.LOAD_RESOURCE_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function loadResourceWorkspaces(groupByType, getWorkspaceDetail) {
    return {
        type: actionTypes.LOAD_RESOURCE_WORKSPACES,
        payload: {
            groupByType,
            getWorkspaceDetail
        }
    }
}

export function loadResourceWorkspacesSuccess(alias, payload, data) {
    return {
        type: actionTypes.LOAD_RESOURCE_WORKSPACES_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function createResourceWorkspace(workspaceData) {
    return {
        type: actionTypes.CREATE_RESOURCE_WORKSPACE,
        payload: {
            workspaceData
        }
    }
}

export function createResourceWorkspaceSuccess(alias, payload, data) {
    return {
        type: actionTypes.CREATE_RESOURCE_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function saveResourceWorkspaceSettings() {
    return {
        type: actionTypes.SAVE_RESOURCE_WORKSPACE_SETTINGS
    }
}

export function digestCreateResourceWorkspace() {
    return {
        type: actionTypes.DIGEST_CREATE_RESOURCE_WORKSPACE
    }
}

export function digestSaveAsNewResourceWorkspace(wsDescription, wsAccessType, wsEditRights, newWorkspaceTemplateGuid, workspaceColourthemeGuid, workspaceCustomColourTheme, doCreate, selectCreatedWorkspace) {
    return {
        type: actionTypes.DIGEST_SAVE_AS_NEW_RESOURCE_WORKSPACE,
        payload: {
            wsDescription,
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            workspaceColourthemeGuid,
            workspaceCustomColourTheme,
            doCreate,
            selectCreatedWorkspace
        }
    };
}

export function addMostRecentlyUsedResourceWorkspace(workspaceGuid, workspaceStructure) {
    return {
        type: actionTypes.ADD_MOST_RECENTLY_USED_RESOURCE_WORKSPACE,
        payload: {
            workspaceGuid,
            workspaceStructure
        }
    }
}

// Job workspace actions
export function digestSelectJobWorkspace(workspaceGuid) {
    return {
        type: actionTypes.DIGEST_SELECT_JOB_WORKSPACE,
        payload: {
            workspaceGuid
        }
    }
}

export function selectJobWorkspace(workspaceGuid) {
    return {
        type: actionTypes.SELECT_JOB_WORKSPACE,
        payload: {
            workspaceGuid
        }
    }
}

export function loadJobWorkspace(workspaceGuid, workspaceChangeUUID, selectWorkspace) {
    return {
        type: actionTypes.LOAD_JOB_WORKSPACE,
        payload: {
            workspaceGuid,
            selectWorkspace,
            workspaceChangeUUID
        }
    }
}

export function loadJobWorkspaceSuccess(alias, payload, data) {
    return {
        type: actionTypes.LOAD_JOB_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function loadJobWorkspaces(groupByType, getWorkspaceDetail) {
    return {
        type: actionTypes.LOAD_JOB_WORKSPACES,
        payload: {
            groupByType,
            getWorkspaceDetail
        }
    }
}

export function loadJobWorkspacesSuccess(alias, payload, data) {
    return {
        type: actionTypes.LOAD_JOB_WORKSPACES_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function createJobWorkspace(workspaceData) {
    return {
        type: actionTypes.CREATE_JOB_WORKSPACE,
        payload: {
            workspaceData
        }
    }
}

export function createJobWorkspaceSuccess(alias, payload, data) {
    return {
        type: actionTypes.CREATE_JOB_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    }
}

export function saveJobWorkspaceSettings() {
    return {
        type: actionTypes.SAVE_JOB_WORKSPACE_SETTINGS
    }
}

export function digestCreateJobWorkspace() {
    return {
        type: actionTypes.DIGEST_CREATE_JOB_WORKSPACE
    }
}

export function digestSaveAsNewJobWorkspace(wsDescription, wsAccessType, wsEditRights, newWorkspaceTemplateGuid, workspaceColourthemeGuid, workspaceCustomColourTheme, doCreate, selectCreatedWorkspace) {
    return {
        type: actionTypes.DIGEST_SAVE_AS_NEW_JOB_WORKSPACE,
        payload: {
            wsDescription,
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            workspaceColourthemeGuid,
            workspaceCustomColourTheme,
            doCreate,
            selectCreatedWorkspace
        }
    };
}

export function addMostRecentlyUsedJobWorkspace(workspaceGuid, workspaceStructure) {
    return {
        type: actionTypes.ADD_MOST_RECENTLY_USED_JOB_WORKSPACE,
        payload: {
            workspaceGuid,
            workspaceStructure
        }
    }
}

// Common workspace management actions
export function setResourceWorkspaceManageWindowVisibility(visible) {
    return {
        type: actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY,
        payload: {
            visible
        }
    }
}

export function setJobWorkspaceManageWindowVisibility(visible) {
    return {
        type: actionTypes.SET_JOB_WORKSPACE_MANAGE_WINDOW_VISIBILITY,
        payload: {
            visible
        }
    }
}

// Lists page workspace initialization
export function loadListPageWorkspaces(groupByType = false, getWorkspaceDetail = true) {
    return {
        type: actionTypes.LOAD_LIST_PAGE_WORKSPACES,
        payload: {
            groupByType,
            getWorkspaceDetail
        }
    }
}
