import { ofType } from 'redux-observable';
import { map, switchMap, withLatestFrom, filter, mergeMap, catchError } from 'rxjs/operators';
import { of, EMPTY } from 'rxjs';
import * as actionTypes from '../actions/actionTypes';
import * as listPageWorkspaceActions from '../actions/listPageWorkspaceActions';
import * as listPageActions from '../actions/listPageActions';
import { TABLE_NAMES } from '../constants/globalConsts';
import { getActiveListView } from '../selectors/listPageSelectors';
import {
    getCurrentViewSelectedWorkspaceGuid,
    getCurrentViewOrderedPrivateWorkspaces,
    getCurrentViewOrderedPublicWorkspaces,
    getCurrentViewMostRecentlyUsedWorkspaces,
    getResourceWorkspaces,
    getJobWorkspaces
} from '../selectors/listPageWorkspaceSelectors';

// Resource workspace epics
export const handleDigestSelectResourceWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_SELECT_RESOURCE_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { workspaceGuid } = action.payload;
            const resourceWorkspaces = getResourceWorkspaces(state);
            
            // Check if workspace is already loaded
            if (resourceWorkspaces.workspacesStructure.map && resourceWorkspaces.workspacesStructure.map[workspaceGuid]) {
                return of(listPageWorkspaceActions.selectResourceWorkspace(workspaceGuid));
            }
            
            // Load workspace if not already loaded
            return of(listPageWorkspaceActions.loadResourceWorkspace(workspaceGuid, null, true));
        })
    );

export const handleSelectResourceWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.SELECT_RESOURCE_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { workspaceGuid } = action.payload;
            const resourceWorkspaces = getResourceWorkspaces(state);
            
            // Add to most recently used
            const workspaceStructure = resourceWorkspaces.workspacesStructure.map && resourceWorkspaces.workspacesStructure.map[workspaceGuid];
            if (workspaceStructure) {
                return of(listPageWorkspaceActions.addMostRecentlyUsedResourceWorkspace(workspaceGuid, workspaceStructure));
            }
            
            return EMPTY;
        })
    );

export const handleDigestCreateResourceWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_CREATE_RESOURCE_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            // Create new workspace with default settings
            const workspaceData = {
                workspace_description: 'New Resource Workspace',
                workspace_accesstype: 'private',
                workspace_editrights: 'owner',
                workspace_type: 'resource'
            };
            
            return of(listPageWorkspaceActions.createResourceWorkspace(workspaceData));
        })
    );

export const handleDigestSaveAsNewResourceWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_SAVE_AS_NEW_RESOURCE_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { wsDescription, wsAccessType, wsEditRights, doCreate, selectCreatedWorkspace } = action.payload;
            
            if (doCreate) {
                const workspaceData = {
                    workspace_description: wsDescription,
                    workspace_accesstype: wsAccessType,
                    workspace_editrights: wsEditRights,
                    workspace_type: 'resource'
                };
                
                return of(listPageWorkspaceActions.createResourceWorkspace(workspaceData));
            }
            
            return EMPTY;
        })
    );

// Job workspace epics
export const handleDigestSelectJobWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_SELECT_JOB_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { workspaceGuid } = action.payload;
            const jobWorkspaces = getJobWorkspaces(state);
            
            // Check if workspace is already loaded
            if (jobWorkspaces.workspacesStructure.map && jobWorkspaces.workspacesStructure.map[workspaceGuid]) {
                return of(listPageWorkspaceActions.selectJobWorkspace(workspaceGuid));
            }
            
            // Load workspace if not already loaded
            return of(listPageWorkspaceActions.loadJobWorkspace(workspaceGuid, null, true));
        })
    );

export const handleSelectJobWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.SELECT_JOB_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { workspaceGuid } = action.payload;
            const jobWorkspaces = getJobWorkspaces(state);
            
            // Add to most recently used
            const workspaceStructure = jobWorkspaces.workspacesStructure.map && jobWorkspaces.workspacesStructure.map[workspaceGuid];
            if (workspaceStructure) {
                return of(listPageWorkspaceActions.addMostRecentlyUsedJobWorkspace(workspaceGuid, workspaceStructure));
            }
            
            return EMPTY;
        })
    );

export const handleDigestCreateJobWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_CREATE_JOB_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            // Create new workspace with default settings
            const workspaceData = {
                workspace_description: 'New Job Workspace',
                workspace_accesstype: 'private',
                workspace_editrights: 'owner',
                workspace_type: 'job'
            };
            
            return of(listPageWorkspaceActions.createJobWorkspace(workspaceData));
        })
    );

export const handleDigestSaveAsNewJobWorkspaceEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.DIGEST_SAVE_AS_NEW_JOB_WORKSPACE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const { wsDescription, wsAccessType, wsEditRights, doCreate, selectCreatedWorkspace } = action.payload;
            
            if (doCreate) {
                const workspaceData = {
                    workspace_description: wsDescription,
                    workspace_accesstype: wsAccessType,
                    workspace_editrights: wsEditRights,
                    workspace_type: 'job'
                };
                
                return of(listPageWorkspaceActions.createJobWorkspace(workspaceData));
            }
            
            return EMPTY;
        })
    );

// Command bar population epic
export const handleCommandBarPopulateWorkspacesSectionEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.COMMAND_BAR_WORKSPACES_SECTION.POPULATE),
        withLatestFrom(state$),
        switchMap(([action, state]) => {
            const activeListView = getActiveListView(state);
            const privateWorkspaces = getCurrentViewOrderedPrivateWorkspaces(state);
            const publicWorkspaces = getCurrentViewOrderedPublicWorkspaces(state);
            const mostRecentWorkspaces = getCurrentViewMostRecentlyUsedWorkspaces(state);
            
            return of(listPageActions.commandBarPopulateWorkspacesSection({
                privateWorkspaces,
                publicWorkspaces,
                mostRecentWorkspaces,
                activeListView
            }));
        })
    );

// Workspace management window visibility epics
export const handleSetResourceWorkspaceManageWindowVisibilityEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY),
        map(action => {
            // This would typically trigger a modal or window to open
            // For now, just return empty to complete the action
            return { type: 'NO_OP' };
        }),
        filter(() => false) // Filter out the NO_OP action
    );

export const handleSetJobWorkspaceManageWindowVisibilityEpic = (action$, state$) =>
    action$.pipe(
        ofType(actionTypes.SET_JOB_WORKSPACE_MANAGE_WINDOW_VISIBILITY),
        map(action => {
            // This would typically trigger a modal or window to open
            // For now, just return empty to complete the action
            return { type: 'NO_OP' };
        }),
        filter(() => false) // Filter out the NO_OP action
    );

// Export all epics
export const listPageWorkspaceEpics = [
    handleDigestSelectResourceWorkspaceEpic,
    handleSelectResourceWorkspaceEpic,
    handleDigestCreateResourceWorkspaceEpic,
    handleDigestSaveAsNewResourceWorkspaceEpic,
    handleDigestSelectJobWorkspaceEpic,
    handleSelectJobWorkspaceEpic,
    handleDigestCreateJobWorkspaceEpic,
    handleDigestSaveAsNewJobWorkspaceEpic,
    handleCommandBarPopulateWorkspacesSectionEpic,
    handleSetResourceWorkspaceManageWindowVisibilityEpic,
    handleSetJobWorkspaceManageWindowVisibilityEpic
];
