import { combineEpics } from 'redux-observable';
import * as simpleDataEpics from './simpleDataEpics';
import * as pagedDataEpics from './pagedDataEpics';
import getPlannerDataEpics from './plannerDataEpics';
import executeLoadPlannerPageEpics$ from './plannerPageLoadExecutionEpics';
import tableViewCellDataEpics from './tableViewEpics';
import getEntityWindowEpics from './entityWindowEpics/index';
import * as tableDataEpics from './tableDataEpics';
import * as tableStructureEpics from './tableStructureEpics';
import * as autocompleteEpics from './autocompleteEpics';
import * as tooltipEpics from './tooltipEpics';
import * as tooltipContextualEpics from './tooltipContextualEpics';

import * as subPagesEpics from './applicationPagesEpics/subPagesEpics';
import {
    createDataGridLoadDataEpic,
    createDataGridChangePageEpic,
    createAddDataGridFieldsEpic,
    createDataGridLoadPagedDataInterceptorEpic,
    createDataGridDetailsPaneCloseEpic,
    createDataGridClearSelectionEpic,
    createJobsPagePagedTableDataBatchUpdateInterceptorEpic,
    entityWindowContextualEditJobPageErrorInterceptor,
    entityWindowClientDeleteJobPageErrorInterceptorEpic,
    entityWindowClientPatchJobPageErrorInterceptorEpic,
    entityWindowJobPatchJobPageErrorInterceptorEpic,
    entityWindowJobDeleteJobPageErrorInterceptorEpic,
    jobsPageDeleteJobSuccessInterceptorEpic,
    jobsPagePatchUpdateJobSuccessInterceptorEpic
} from './dataGridPageDataEpics';
import { createFieldControlEpic } from './fieldControlEpics';
import { ajax } from 'rxjs/ajax';
import getAdminDataEpics from './adminSettings/adminSettingDataEpics';
import { createMultiSelectFilterInputEpic, createAddDataGridCustomFieldFiltersEpic, createPopulateFilterMultiValuesEpic } from './filterPaneEpics';
import { createSkillsFilterLoadEpic } from './populateSkillFilterValuesEpics';
import getWorkspaceEpics from './workspaceEpics';
import { listPageWorkspaceEpics } from './listPageWorkspaceEpics';
import skillStructureEpics from './skillStructureEpics';
import { PLANNER_MASTER_REC_ALIAS, PLANNER_PAGE_ALIAS, PLANNERPAGE_FILTER_ALIAS, PLANNER_TABLE_DATAS_SUFFIX } from '../constants/plannerConsts';
import { MARKETPLACE_PAGE_ALIAS, MARKETPLACE_PAGE_FILTER_ALIAS } from '../constants/marketplacePageConsts';
import { DATA_GRID_COLUMNS_CHANGE, PAGE_ACTIONS, DATA_GRID_PAGE_CHANGE, PLANNER_DATA_LOADED } from '../actions/actionTypes';
import applicationPagesEpics from './applicationPagesEpics';
import applicationUserEpics from './applicationUserEpics';
import talentProfileEpics from './talentProfileEpics';
import reportEpics from './reportEpic';
import adminSettingsConsumerEpics from './adminSettingsConsumerEpics';
import { ENTITY_WINDOW_MODULES } from '../constants/entityWindowConsts';
import { JOBSPAGE_FILTER_ALIAS, JOBS_PAGE_ALIAS, JOBS_PAGE_DP_ALIAS, JOB_PAGE_PAGED_DATA, ROLE_GROUP_DETAILS_PAGE, ROLE_GROUP_LIST_PAGE } from '../constants/jobsPageConsts';
import applicationNavigationEpics from './applicationNavigationEpics';
import promptContextEpics from './promptContextEpics';
import { loadLinkedDatasEpic, createLoadDefaultLinkedFieldsEpic } from './linkedDataEpics';
import { setPlannerFieldsOptionsProps } from '../actions/workspaceSettingsActions';
import getAvatarEpics from './avatarEpics';
import plannerResourceSkillsEpics from './plannerResourceSkillsEpics';
import commentsEpics from './commentsEpics/commentsEpics';
import { DEFAULT_JOBS_PAGE_LINKED_FIELDS, DEFAULT_MARKETPLACE_PAGE_LINKED_FIELDS, DEFAULT_PLANNER_PAGE_LINKED_FIELDS, DEFAULT_ROLE_INBOX_PAGE_LINKED_FIELDS } from '../constants/tablesConsts';
import getEntityStructureEpics from './entityStructureEpic';
import applicationHelpEpics from './applicationHelpEpics';
import detailsPaneEpics from './detailsPaneEpics';
import jobPageDetailsPaneEpics from './jobPageDetailsPaneEpics';
import auditEpics from './auditEpics';
import editResourceSkillsWindowEpics from './editResourceSkillsWindowEpics';
import resourceSkillsEpics from './resourceSkillsEpics';
import loadTranslationMessages from './internationalizationEpics';
import attachmentsEpics from './attachmentsEpics';
import batchingInterceptEpics from './batchingInterceptEpics';
import getUserEntityAccessEpics from './userEntityAccessEpics';
import { MAX_DATA_GRID_VISIBLE_RECORDS, MAX_DATA_GRID_OPERATION_LOG_VISIBLE_RECORDS, TABLE_NAMES, WIDGET_ALIAS } from '../constants/globalConsts';
import { batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError, patchPagedDataSuccess } from '../actions/pagedDataActions';
import roleTransitionDialogsEpics from './roleTransitionDialogsEpics';
import roleGroupListPageEpics from './roleGroupListEpics';
import roleGroupDetailsPageEpics from './roleGroupDetailsPageEpics';
import previewEntityPageEpics from './previewEntityPageEpics';
import loadRolerequestsEpics from './loadRolerequestsEpics';
import requirementsEpics from './requirementsEpics';
import suggestedResourcesEpics from './suggestedResourcesEpics';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_DP_ALIAS, ROLE_INBOX_PAGE_FILTER_ALIAS, ROLE_INBOX_PAGE_PAGED_DATA, ROLE_INBOX_PAGE_TABLE_DATA_ALIAS } from '../constants/roleInboxPageConsts';
import rolerequestEpics from './rolerequestEpics';
import roleInboxPageEpics from './roleInboxPageEpics';
import marketplacePageEpics from './marketplaceEpics';
import { reloadRoleInboxPageDataGridAction } from '../actions/dataGridActions';
import { batchDeleteTableDataError, batchDigestDeleteTableDataSuccess, deleteTableDataSuccess, digestPatchTableDataSuccess, loadMoreTableDataSuccess } from '../actions/tableDataActions';
import notificationPageDataEpic from './notificationsPageDataEpics';
import notificationServiceEpic from './notificationEpics/notificationServiceEpic';
import timesheetServiceEpic from './timesheetEpics/timesheetServiceEpic';
import { createUnsavedChangesPromptEpic } from './promptContextEpics/unsavedChangesPromptEpic';
import rollForwardDialogEpic from './rollForwardDialogEpics';
import jobDuplicateEpics from './jobDuplicateEpics';
import roleGroupDuplicateEpics from './roleGroupDuplicateEpics';
import { ROLL_FORWARD_DIALOG_ALIAS } from '../constants/rollForwardConst';
import { PEOPLE_FINDER_DIALOG_FILTER_ALIAS, PEOPLE_FINDER_DIALOG_ALIAS, PEOPLE_FINDER_DIALOG_PAGED_DATA, PEOPLE_FINDER_DIALOG_INITIAL_SELECTION_FIELDS } from '../constants/peopleFinderConst';
import { getPeopleFinderSelectionCalcFields } from '../utils/peopleFinderQuerySelection';
import peoplFinderEpics from './peopleFinderEpics';
import { criteriaRoleBudgetEpics$ } from './criteriaRoleBudgetEpics';
import notificationSettingsEpic from './notificationsSettingsEpics';
import { NOTIFICATIONS_PAGE_TABLE_DATA_ALIAS, NOTIFICATION_SETTINGS_USER_PAGE } from '../constants/notificationsPageConsts';
import { AliasConstants } from '../constants/adminSettingConsts';
import { PREVIEW_ENTITY_PAGE_ALIAS } from '../constants/marketplacePageConsts';
import { workHistoryPageEpics, workHistoryTalentProfilePageEpics } from './workHistoryEpics';
import { TALENT_PROFILE_ALIAS } from '../constants/talentProfileConsts';
import { TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../constants/tableViewPageConsts';
import { DATA_GRID_FILTERS_SUFFIX, DATA_GRID_TABLE_DATAS_SUFFIX } from '../constants/dataGridConsts';
import { API_KEYS } from '../constants/apiConsts';
import { getMarketplaceAdditionalSuccessActions } from '../utils/dataGridUtils';
import roleTemplatesEpics from './roleTemplatesEpics';
import { defaultAppliedMarketplaceFiltersSection } from '../state/marketplacePage';
import { ROLE_FROM_TEMPLATE_ALIAS, ROLE_GROUP_AUTOCOMPLETE_ALIAS } from '../constants/rolesConsts';
import tableViewDataEpics from './tableViewDataEpics';
import educationSectionEpics from './educationSectionEpics';
import experienceSectionEpics from './experienceSection';
import legendEpics from './legendEpics';
import { JOB_FILTER_DIALOG_ALIAS, JOB_FILTER_DIALOG_PAGED_DATA, JOB_FILTER_DIALOG_FILTER_ALIAS } from '../constants/jobFilterDialogConsts';
import { MASS_DUPLICATE_JOBS_PAGED_DATA, MASS_DUPLICATE_JOBS_FILTER_ALIAS } from '../constants/massDuplicateJobsConsts';
import { combineAPIEpics } from './middlewareExtensions';
import authenticationEpics from './authenticationEpics';
import rotateEpic from './rotateViewEpics/plannerPageRotateEpics';
import { JOB_DUPLICATE_DIALOG_ALIAS } from '../constants/jobDuplicateConsts';
import pollLongRunningTask from './longRunningTaskEpics';
import progressInticatorEpics from './progressIndicatorEpics';
import { ROLE_GROUP_LIST_PAGE_TABLE_DATA_ALIAS } from '../constants/rolegroupListPageConsts';
import { ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS } from '../constants';
import { ROLE_GROUP_DUPLICATE_DIALOG_ALIAS } from '../constants/roleGroupDuplicateConsts';
import { OPERATION_LOG_API_NAME, OPERATION_LOG_DIALOG_ALIAS, OPERATION_LOG_DIALOG_PAGED_DATA } from '../constants/operationLogDialogConsts';
import operationsLogEpics, { openOperationsLogDialogEpic } from './operationsLogEpics';
import { getPlannerPageFilters } from '../selectors/plannerPageSelectors';
import massDuplicateJobsEpics from '../epics/adminSettings/massDuplicateJobsEpics';
import repeatBookingEpics from './repeatBookingEpics';
import { fetchOptionsEpic } from './configurableSelectEpics';
import summaryPageEpics from './summaryPageEpics';
import combinedFilterValueChangeEpic from './filtersEpics/filterValueChangeEpic';
import { RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_DP_ALIAS, RESOURCES_PAGE_FILTER_ALIAS, RESOURCES_PAGE_PAGED_DATA } from '../constants/resourcesPageConsts';

const jobsPageLoadAdditionalFieldsActionHandler = `${DATA_GRID_COLUMNS_CHANGE}_${JOBS_PAGE_ALIAS}`;
const roleInboxPageLoadAdditionalFieldsActionHandler = `${DATA_GRID_COLUMNS_CHANGE}_${ROLE_INBOX_PAGE_ALIAS}`;
const resourcesPageLoadAdditionalFieldsActionHandler = `${DATA_GRID_COLUMNS_CHANGE}_${RESOURCES_PAGE_ALIAS}`;

const {
    PLANNER_PAGE_MODAL,
    PLANNER_PAGE_DETAILS_PANE,
    JOBS_PAGE_MODAL,
    JOBS_PAGE_DETAILS_PANE,
    RESOURCES_PAGE_MODAL,
    RESOURCES_PAGE_DETAILS_PANE,
    PLANNER_PAGE_MODAL_SIMPLIFIED,
    PLANNER_PAGE_BATCH_MODAL,
    PLANNER_PAGE_BATCH_DETAILS_PANE,
    PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL,
    JOBS_PAGE_BATCHED_DETAILS_PANE,
    JOBS_PAGE_BATCH_MODAL,
    RESOURCES_PAGE_BATCHED_DETAILS_PANE,
    RESOURCES_PAGE_BATCH_MODAL,
    JOBS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_REQUEST_FORM,
    JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE,
    JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE,
    JOBS_PAGE_RESOURCE_DETAILS_PANE,
    ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL,
    ROLE_INBOX_PAGE_BATCH_MODAL,
    ROLE_INBOX_PAGE_DETAILS_PANE,
    ROLE_INBOX_PAGE_BATCH_DETAILS_PANE,
    ROLE_INBOX_PAGE_MODAL_SIMPLIFIED,
    ROLE_INBOX_ASSIGNEES_BUDGET_MODAL,
    ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL,
    ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL,
    ROLE_DETAILS_PAGE_MOVE_TO_PROMPT,
    PLANNER_PAGE_MOVE_TO_PROMPT,
    NOTIFICATION_PAGE_MODAL,
    MARKETPLACE_DETAILS_PANE,
    PROFILE_PAGE_MODAL,
    PREVIEW_ENTITY_MODAL,
    CREATE_ROLE_TEMPLATE_MODAL,
    TABLE_VIEW_MODAL,
    TABLE_VIEW_MODAL_SIMPLIFIED,
    GLOBAL_CREATE_MODAL
} = ENTITY_WINDOW_MODULES;

const rootEpic = (...args) => combineEpics(
    authenticationEpics(),
    applicationPagesEpics,
    applicationUserEpics,
    talentProfileEpics,
    roleGroupListPageEpics(),
    roleGroupDetailsPageEpics,
    previewEntityPageEpics,
    loadRolerequestsEpics,
    tableViewDataEpics(),
    requirementsEpics,
    suggestedResourcesEpics,
    reportEpics(),
    adminSettingsConsumerEpics,
    //Stub
    simpleDataEpics.createLoadDataEpic('stub'),
    simpleDataEpics.createLoadValueEpic('stub'),
    simpleDataEpics.createCreateEpic('stub'),
    simpleDataEpics.createUpdateDataEntryEpic('stub'),
    simpleDataEpics.createDeleteDataEntryEpic('stub'),
    tableDataEpics.createLoadTableDataEpic('stub')(),
    tableDataEpics.createInsertTableDataEpic('stub')(),
    tableDataEpics.createDeleteTableDataEpic('stub')(),
    tableDataEpics.createUpdateTableDataEpic('stub')(),
    tableDataEpics.createPatchTableDataEpic('stub')(),
    //Bookings
    simpleDataEpics.createLoadDataEpic('booking'),
    simpleDataEpics.createUpdateDataEntryEpic('booking'),
    simpleDataEpics.createCreateEpic('booking'),
    simpleDataEpics.createDeleteDataEntryEpic('booking'),
    //Paged data
    pagedDataEpics.createLoadPagedAccessDataEpic(PLANNER_MASTER_REC_ALIAS),
    pagedDataEpics.createLoadPagedResultsDataEpic(PLANNER_MASTER_REC_ALIAS),
    //Planner Data
    getPlannerDataEpics(),
    executeLoadPlannerPageEpics$,
    rotateEpic,
    //Table View Cell Data Epics
    tableViewCellDataEpics,
    // Skill Epics
    plannerResourceSkillsEpics,
    //Entity window
    getEntityWindowEpics(),
    criteriaRoleBudgetEpics$,
    //RoleGroupDetails
    createUnsavedChangesPromptEpic(ROLE_REQUEST_FORM),
    //Table structure
    tableStructureEpics.loadTableFieldInfosEpic,
    tableStructureEpics.loadTableInfosEpic,
    //Entity Structure
    getEntityStructureEpics,
    //Autocomplete
    autocompleteEpics.createLoadAutocompleteDataEpic('simpleFilters'),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_MODAL_SIMPLIFIED),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_BATCH_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_BATCH_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_MOVE_TO_PROMPT),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNER_PAGE_ASSIGNEES_BUDGET_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_BATCHED_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_BATCH_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_ROLE_GROUP_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(RESOURCES_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(RESOURCES_PAGE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(RESOURCES_PAGE_BATCHED_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(RESOURCES_PAGE_BATCH_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_FROM_TEMPLATE_ALIAS),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_GROUP_AUTOCOMPLETE_ALIAS),

    autocompleteEpics.createLoadAutocompleteDataEpic('entity_lookup_window'),
    autocompleteEpics.createLoadAutocompleteDataEpic('talentProfile'),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_REQUEST_FORM),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOBS_PAGE_RESOURCE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_BATCH_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_MODAL_SIMPLIFIED),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_ASSIGNEES_BUDGET_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_PUBLISH_ROLE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_INBOX_PAGE_EDIT_ROLE_PUBLICATION_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_DETAILS_PAGE_MOVE_TO_PROMPT),
    autocompleteEpics.createLoadAutocompleteDataEpic(NOTIFICATION_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLL_FORWARD_DIALOG_ALIAS),
    autocompleteEpics.createLoadAutocompleteDataEpic(JOB_DUPLICATE_DIALOG_ALIAS),
    autocompleteEpics.createLoadAutocompleteDataEpic(MARKETPLACE_DETAILS_PANE),
    autocompleteEpics.createLoadAutocompleteDataEpic(PROFILE_PAGE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(PREVIEW_ENTITY_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(CREATE_ROLE_TEMPLATE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(TABLE_VIEW_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(TABLE_VIEW_MODAL_SIMPLIFIED),
    autocompleteEpics.createLoadAutocompleteDataEpic(GLOBAL_CREATE_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(ROLE_GROUP_DUPLICATE_DIALOG_ALIAS),
    autocompleteEpics.createLoadAutocompleteDataEpic(ENTITY_WINDOW_MODULES.ROLE_GROUP_MODAL),
    autocompleteEpics.createLoadAutocompleteDataEpic(PLANNERPAGE_FILTER_ALIAS),

    //admin setting
    getAdminDataEpics(),

    //tooltips
    tooltipEpics.createShowTooltipEpic(PLANNER_PAGE_ALIAS),

    //tooltip contextual epics
    tooltipContextualEpics.createContextualTooltipEpic(PLANNER_PAGE_ALIAS),
    tooltipContextualEpics.createContextualTooltipEpic(TABLE_VIEW_PAGE_ALIAS),

    // jobs page
    createDataGridLoadDataEpic(JOBS_PAGE_ALIAS),
    pagedDataEpics.createPagedDataKeepAliveEpic(JOB_PAGE_PAGED_DATA),
    createDataGridChangePageEpic(JOBS_PAGE_ALIAS),
    createDataGridDetailsPaneCloseEpic(JOBS_PAGE_ALIAS, JOBS_PAGE_DP_ALIAS),
    createDataGridClearSelectionEpic(JOBS_PAGE_ALIAS, TABLE_NAMES.JOB),

    createAddDataGridFieldsEpic(
        JOBS_PAGE_ALIAS,
        jobsPageLoadAdditionalFieldsActionHandler
    ),
    tableDataEpics.createBatchPatchTableDataEpic(TABLE_NAMES.JOB, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),
    createJobsPagePagedTableDataBatchUpdateInterceptorEpic(TABLE_NAMES.JOB),
    jobsPageDeleteJobSuccessInterceptorEpic,
    jobsPagePatchUpdateJobSuccessInterceptorEpic,
    entityWindowContextualEditJobPageErrorInterceptor,
    entityWindowClientDeleteJobPageErrorInterceptorEpic,
    entityWindowClientPatchJobPageErrorInterceptorEpic,
    entityWindowJobPatchJobPageErrorInterceptorEpic,
    entityWindowJobDeleteJobPageErrorInterceptorEpic,

    // role inbox page
    roleInboxPageEpics(),
    createDataGridLoadDataEpic(ROLE_INBOX_PAGE_ALIAS),
    createDataGridChangePageEpic(ROLE_INBOX_PAGE_ALIAS),
    createDataGridDetailsPaneCloseEpic(ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_DP_ALIAS),
    createDataGridClearSelectionEpic(ROLE_INBOX_PAGE_ALIAS, TABLE_NAMES.ROLEREQUEST),
    createAddDataGridFieldsEpic(ROLE_INBOX_PAGE_ALIAS, roleInboxPageLoadAdditionalFieldsActionHandler),

    //Marketplace page
    marketplacePageEpics(),
    createDataGridLoadDataEpic(
        MARKETPLACE_PAGE_ALIAS,
        undefined,
        false,
        API_KEYS.ROLE_REQUEST_API_KEY,
        (data) => data.map(dataEntry => dataEntry.body),
        getMarketplaceAdditionalSuccessActions,
        defaultAppliedMarketplaceFiltersSection[TABLE_NAMES.ROLEREQUEST]
    ),
    createDataGridChangePageEpic(MARKETPLACE_PAGE_ALIAS, API_KEYS.ROLE_REQUEST_API_KEY, undefined, defaultAppliedMarketplaceFiltersSection[TABLE_NAMES.ROLEREQUEST]),
    pagedDataEpics.createPagedDataKeepAliveEpic(`${MARKETPLACE_PAGE_ALIAS}_${DATA_GRID_FILTERS_SUFFIX}`, undefined, API_KEYS.ROLE_REQUEST_API_KEY),

    // notifications page
    notificationPageDataEpic,
    notificationServiceEpic,
    notificationSettingsEpic,

    pagedDataEpics.createPagedDataKeepAliveEpic(ROLE_INBOX_PAGE_PAGED_DATA),
    pagedDataEpics.createPatchPagedDataEpic(ROLE_INBOX_PAGE_PAGED_DATA, patchPagedDataSuccess)(),

    tableDataEpics.createInsertTableDataEpic(ROLE_INBOX_PAGE_PAGED_DATA, reloadRoleInboxPageDataGridAction)(),
    tableDataEpics.createDeleteTableDataEpic(ROLE_INBOX_PAGE_PAGED_DATA, deleteTableDataSuccess)(),
    tableDataEpics.createBatchDeleteTableDataEpic(ROLE_INBOX_PAGE_PAGED_DATA, batchDigestDeleteTableDataSuccess, batchDeleteTableDataError)(),

    tableDataEpics.createPatchTableDataEpic(TABLE_NAMES.JOB, digestPatchTableDataSuccess)(),
    tableDataEpics.createPatchTableDataEpic(TABLE_NAMES.RESOURCE, digestPatchTableDataSuccess)(),
    tableDataEpics.createBatchPatchTableDataEpic(ROLE_INBOX_PAGE_PAGED_DATA, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),
    tableDataEpics.createBatchPatchTableDataEpic(TABLE_NAMES.ROLEREQUEST, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),

    tableDataEpics.createBatchPatchTableDataEpic(TABLE_NAMES.JOB, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),
    tableDataEpics.createBatchPatchTableDataEpic(TABLE_NAMES.RESOURCE, batchDigestPatchPagedTableDataSuccess, batchPatchPagedTableDataError)(),

    // resources page
    createDataGridLoadDataEpic(RESOURCES_PAGE_ALIAS),
    pagedDataEpics.createPagedDataKeepAliveEpic(RESOURCES_PAGE_PAGED_DATA),
    createDataGridChangePageEpic(RESOURCES_PAGE_ALIAS),
    createDataGridDetailsPaneCloseEpic(RESOURCES_PAGE_ALIAS, RESOURCES_PAGE_DP_ALIAS),
    createDataGridClearSelectionEpic(RESOURCES_PAGE_ALIAS, TABLE_NAMES.RESOURCE),

    createAddDataGridFieldsEpic(
        RESOURCES_PAGE_ALIAS,
        resourcesPageLoadAdditionalFieldsActionHandler
    ),

    // planner filters
    createPopulateFilterMultiValuesEpic(
        PLANNERPAGE_FILTER_ALIAS,
        [PLANNER_DATA_LOADED],
        getPlannerPageFilters,
        `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`
    ),
    createMultiSelectFilterInputEpic(PLANNERPAGE_FILTER_ALIAS),
    combinedFilterValueChangeEpic,

    // jobsPageFilters
    createAddDataGridCustomFieldFiltersEpic(JOBSPAGE_FILTER_ALIAS, TABLE_NAMES.JOB),
    createMultiSelectFilterInputEpic(JOBSPAGE_FILTER_ALIAS),
    createDataGridLoadPagedDataInterceptorEpic(JOBS_PAGE_ALIAS),

    // roleInboxFilters
    createAddDataGridCustomFieldFiltersEpic(ROLE_INBOX_PAGE_FILTER_ALIAS, TABLE_NAMES.ROLEREQUEST),
    createMultiSelectFilterInputEpic(ROLE_INBOX_PAGE_FILTER_ALIAS),
    createDataGridLoadPagedDataInterceptorEpic(ROLE_INBOX_PAGE_ALIAS),

    // marketplaceFilters
    createAddDataGridCustomFieldFiltersEpic(MARKETPLACE_PAGE_FILTER_ALIAS, TABLE_NAMES.ROLEREQUEST),
    createMultiSelectFilterInputEpic(MARKETPLACE_PAGE_FILTER_ALIAS),
    createDataGridLoadPagedDataInterceptorEpic(MARKETPLACE_PAGE_ALIAS),

    // table view filters
    createMultiSelectFilterInputEpic(TABLE_VIEW_PAGE_FILTER_ALIAS),
    createSkillsFilterLoadEpic(TABLE_VIEW_PAGE_FILTER_ALIAS),

    // peopleFinderFilters
    createMultiSelectFilterInputEpic(PEOPLE_FINDER_DIALOG_FILTER_ALIAS),

    // peopleFinderDataGridPageData
    createDataGridLoadDataEpic(PEOPLE_FINDER_DIALOG_ALIAS, getPeopleFinderSelectionCalcFields, undefined, undefined, undefined, undefined, undefined,
        undefined, PEOPLE_FINDER_DIALOG_INITIAL_SELECTION_FIELDS),
    pagedDataEpics.createPagedDataKeepAliveEpic(PEOPLE_FINDER_DIALOG_PAGED_DATA),
    createDataGridChangePageEpic(PEOPLE_FINDER_DIALOG_ALIAS),
    createDataGridClearSelectionEpic(PEOPLE_FINDER_DIALOG_ALIAS, TABLE_NAMES.RESOURCE, [`${DATA_GRID_PAGE_CHANGE}_${PEOPLE_FINDER_DIALOG_ALIAS}`]),
    createDataGridLoadPagedDataInterceptorEpic(PEOPLE_FINDER_DIALOG_ALIAS),
    peoplFinderEpics,

    // jobFilterDialog
    combineAPIEpics(
        createDataGridLoadDataEpic(JOB_FILTER_DIALOG_ALIAS, undefined, false, undefined, undefined, undefined, undefined, MAX_DATA_GRID_VISIBLE_RECORDS),
        pagedDataEpics.createPagedDataKeepAliveEpic(JOB_FILTER_DIALOG_PAGED_DATA),
        createDataGridChangePageEpic(JOB_FILTER_DIALOG_ALIAS, undefined, undefined, undefined, false),
        createDataGridClearSelectionEpic(JOB_FILTER_DIALOG_ALIAS, TABLE_NAMES.JOB, [`${DATA_GRID_PAGE_CHANGE}_${JOB_FILTER_DIALOG_ALIAS}`]),
        createDataGridLoadPagedDataInterceptorEpic(JOB_FILTER_DIALOG_ALIAS),
        createAddDataGridCustomFieldFiltersEpic(JOB_FILTER_DIALOG_FILTER_ALIAS, TABLE_NAMES.JOB),
        createMultiSelectFilterInputEpic(JOB_FILTER_DIALOG_FILTER_ALIAS)
    ),

    // massDuplicateJobs
    pagedDataEpics.createPagedDataKeepAliveEpic(MASS_DUPLICATE_JOBS_PAGED_DATA),
    createMultiSelectFilterInputEpic(MASS_DUPLICATE_JOBS_FILTER_ALIAS),
    createAddDataGridCustomFieldFiltersEpic(MASS_DUPLICATE_JOBS_FILTER_ALIAS, TABLE_NAMES.JOB),
    massDuplicateJobsEpics,

     // resourcesPageFilters
    createAddDataGridCustomFieldFiltersEpic(RESOURCES_PAGE_FILTER_ALIAS, TABLE_NAMES.RESOURCE),
    createMultiSelectFilterInputEpic(RESOURCES_PAGE_FILTER_ALIAS),
    createDataGridLoadPagedDataInterceptorEpic(RESOURCES_PAGE_ALIAS),

    // operationsLogDialog
    combineAPIEpics(
        createDataGridLoadDataEpic(OPERATION_LOG_DIALOG_ALIAS, undefined, false, OPERATION_LOG_API_NAME, undefined, undefined, undefined, MAX_DATA_GRID_OPERATION_LOG_VISIBLE_RECORDS),
        pagedDataEpics.createPagedDataKeepAliveEpic(OPERATION_LOG_DIALOG_PAGED_DATA, undefined, OPERATION_LOG_API_NAME),
        createDataGridClearSelectionEpic(OPERATION_LOG_DIALOG_ALIAS, OPERATION_LOG_API_NAME, [`${DATA_GRID_PAGE_CHANGE}_${OPERATION_LOG_DIALOG_ALIAS}`]),
        createDataGridChangePageEpic(OPERATION_LOG_DIALOG_ALIAS, OPERATION_LOG_API_NAME, undefined, undefined, false),
        createDataGridLoadPagedDataInterceptorEpic(OPERATION_LOG_DIALOG_ALIAS),
        openOperationsLogDialogEpic,
        operationsLogEpics
    ),

    // repeatBooking
    repeatBookingEpics,

    //workHistory
    workHistoryTalentProfilePageEpics(TALENT_PROFILE_ALIAS),
    workHistoryPageEpics(),

    //workspaces
    getWorkspaceEpics(),
    skillStructureEpics,

    //Timesheet
    timesheetServiceEpic,

    //field controls
    createFieldControlEpic(PLANNER_PAGE_DETAILS_PANE),
    createFieldControlEpic(PLANNER_PAGE_BATCH_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_DETAILS_PANE),
    createFieldControlEpic(RESOURCES_PAGE_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_ROLE_GROUP_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_BATCHED_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_ROLEGROUPLIST_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_ROLEGROUPLIST_BATCHED_DETAILS_PANE),
    createFieldControlEpic(JOBS_PAGE_RESOURCE_DETAILS_PANE),
    createFieldControlEpic(ROLE_DETAILS_PAGE_ROLE_GROUP_DETAILS_PANE),
    createFieldControlEpic(ROLE_INBOX_PAGE_DETAILS_PANE),
    createFieldControlEpic(ROLE_INBOX_PAGE_BATCH_DETAILS_PANE),
    createFieldControlEpic(MARKETPLACE_DETAILS_PANE),

    //sub pages
    subPagesEpics.createOpenSubPageEpic(ROLE_GROUP_DETAILS_PAGE),
    subPagesEpics.createOpenSubPageEpic(ROLE_GROUP_LIST_PAGE),
    subPagesEpics.createOpenSubPageEpic(PREVIEW_ENTITY_PAGE_ALIAS),
    subPagesEpics.createLoadSubPageEpic(ROLE_GROUP_DETAILS_PAGE),
    subPagesEpics.createLoadSubPageEpic(ROLE_GROUP_LIST_PAGE),
    subPagesEpics.createLoadSubPageEpic(PREVIEW_ENTITY_PAGE_ALIAS),
    subPagesEpics.createCloseSubPageEpic(ROLE_GROUP_DETAILS_PAGE),
    subPagesEpics.createCloseSubPageEpic(ROLE_GROUP_LIST_PAGE),

    // commentsEpics
    commentsEpics,

    // App navigation
    applicationNavigationEpics,

    //PromptContext
    promptContextEpics(),

    // Linked datas
    loadLinkedDatasEpic(
        PLANNER_PAGE_ALIAS,
        (_tableDatasAlias, changedFields, action) => {
            return setPlannerFieldsOptionsProps(
                action.payload.workspaceGuid,
                changedFields,
                {
                    loaded: true
                }
            );
        }
    ),

    loadLinkedDatasEpic(JOBS_PAGE_ALIAS),

    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.plannerPage,
        `${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
        DEFAULT_PLANNER_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.jobsPage,
        `${JOBS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`,
        DEFAULT_JOBS_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.rolegroupListPage,
        `${ROLE_GROUP_LIST_PAGE}_${DATA_GRID_TABLE_DATAS_SUFFIX}`,
        DEFAULT_JOBS_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.rolegroupDetailsPage,
        ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS,
        DEFAULT_JOBS_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.roleInboxPage,
        `${ROLE_INBOX_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`,
        DEFAULT_ROLE_INBOX_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.marketplacePage,
        `${MARKETPLACE_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`,
        DEFAULT_MARKETPLACE_PAGE_LINKED_FIELDS
    ),
    createLoadDefaultLinkedFieldsEpic(
        PAGE_ACTIONS.OPEN.tableViewPage,
        `${TABLE_VIEW_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`,
        DEFAULT_PLANNER_PAGE_LINKED_FIELDS
    ),

    //Avatars
    getAvatarEpics(),

    //App Help
    applicationHelpEpics,

    //DetailsPane
    detailsPaneEpics(),

    //jobPageDetailsPaneEpics
    jobPageDetailsPaneEpics(),

    //i18n
    loadTranslationMessages(),

    auditEpics,
    editResourceSkillsWindowEpics,
    resourceSkillsEpics,
    attachmentsEpics,

    batchingInterceptEpics(),
    //User entity access
    getUserEntityAccessEpics(),

    roleTransitionDialogsEpics(),

    // Load role request related epics
    rolerequestEpics(),

    //roll forward dialog epics
    rollForwardDialogEpic,
    tableDataEpics.createLoadTableDataEpic(ROLL_FORWARD_DIALOG_ALIAS)(),
    tableDataEpics.createLoadTableDataEpic(NOTIFICATION_SETTINGS_USER_PAGE)(),
    tableDataEpics.createLoadTableDataEpic(AliasConstants.ADMIN_NOTIFICATIONS_SETTINGS)(),
    tableDataEpics.createLoadTableDataEpic(`${PLANNER_PAGE_ALIAS}_${PLANNER_TABLE_DATAS_SUFFIX}`, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(`${JOBS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(`${RESOURCES_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(NOTIFICATIONS_PAGE_TABLE_DATA_ALIAS, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(`${ROLE_GROUP_LIST_PAGE_TABLE_DATA_ALIAS}`, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(`${ROLE_GROUP_DETAILS_PAGE_TABLE_DATA_ALIAS}`, loadMoreTableDataSuccess)(),
    tableDataEpics.createLoadTableDataEpic(`${ROLE_INBOX_PAGE_TABLE_DATA_ALIAS}`, loadMoreTableDataSuccess)(),

    //job duplicate dialog epics
    jobDuplicateEpics,
    tableDataEpics.createLoadTableDataEpic(JOB_DUPLICATE_DIALOG_ALIAS)(),

    //role group duplicate dialog epics
    roleGroupDuplicateEpics,
    tableDataEpics.createLoadTableDataEpic(ROLE_GROUP_DUPLICATE_DIALOG_ALIAS)(),

    //role templates epics
    roleTemplatesEpics,

    //education section epics
    educationSectionEpics(),

    // experience section epics
    experienceSectionEpics(),
    legendEpics(),
    pollLongRunningTask,
    progressInticatorEpics,
    fetchOptionsEpic,

    //summary page epics
    pagedDataEpics.createPagedDataKeepAliveEpic(WIDGET_ALIAS.UPCOMING_BOOKINGS_DETAILS),
    pagedDataEpics.createPagedDataKeepAliveEpic(WIDGET_ALIAS.UNASSIGNED_BOOKINGS_DETAILS),
    pagedDataEpics.createPagedDataKeepAliveEpic(WIDGET_ALIAS.JOBS_OVER_BUDGET_DETAILS),
    pagedDataEpics.createPagedDataKeepAliveEpic(WIDGET_ALIAS.ACTION_REQUIRED),
    pagedDataEpics.createPagedDataKeepAliveEpic(WIDGET_ALIAS.YOUR_REQUESTS),
    summaryPageEpics,

    // Lists page workspace epics
    ...listPageWorkspaceEpics
)(...args, { ajax });

export default rootEpic;