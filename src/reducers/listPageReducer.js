import initialState from '../state/initialState';
import { LIST_PAGE_ACTIONS } from '../actions/actionTypes';
import listPageWorkspacesReducer from './listPageReducer/workspacesReducer';

export default (state = initialState.listPage, action) => {
    // Handle workspace actions first
    const workspaceState = listPageWorkspacesReducer(state, action);
    if (workspaceState !== state) {
        return workspaceState;
    }

    switch (action.type) {
        case LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW: {
            return {
                ...state,
                activeListView: action.payload
            }
        }
        default: {
            return state;
        }

    }
};