import { COMMAND_BAR_PLANS_SECTION, COMMAND_BAR_WORKSPACES_SECTION, JOBS_COMMAND_BAR, SET_CONFIG } from "../../actions/actionTypes";
import { COMMAND_BAR_MENUS_SECTION_KEYS } from "../../constants";
import workspacesSectionReducer from "../../state/commandBar/listWorkspacesSection";
import { getMenusSectionIndexByKey } from "../../utils/commandBarUtils";
import initialState from "../../state/initialState";

export default function commandBarReducer(state = initialState.jobsPage.commandBarConfig, action) {

    switch (action.type) {
        case JOBS_COMMAND_BAR.SET_SECTION_VISIBILITY: {
            const { sectionKey, visible } = action.payload;
            const sectionIndex = getMenusSectionIndexByKey(state.menusSection, sectionKey);

            return {
                ...state,
                menusSection: state.menusSection.map((section, index) => {
                    if (index === sectionIndex) {
                        return {
                            ...section,
                            visible
                        }
                    }
                    return {
                        ...section,
                        visible: false
                    }
                })
            };
        }
        case COMMAND_BAR_WORKSPACES_SECTION.POPULATE: {
            const plansSectionIndex = getMenusSectionIndexByKey(state.menusSection, COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);

            return {
                ...state,
                menusSection: [
                    ...state.menusSection.slice(0, plansSectionIndex),
                    workspacesSectionReducer(state.menusSection[plansSectionIndex], action),
                    ...state.menusSection.slice(plansSectionIndex + 1)
                ]
            };
        }
        case SET_CONFIG: {
            const { config } = action.payload;
            const plansSectionIndex = getMenusSectionIndexByKey(state.menusSection, COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);

            let commandBarConfig = config;
            if (config.menusSection[plansSectionIndex].hasOwnProperty('items')) {
                commandBarConfig = {
                    ...commandBarConfig,
                    menusSection: [
                        ...commandBarConfig.menusSection.slice(0, plansSectionIndex),
                        workspacesSectionReducer(commandBarConfig.menusSection[plansSectionIndex], action),
                        ...commandBarConfig.menusSection.slice(plansSectionIndex + 1)
                    ]
                }
            }
            return {
                ...state,
                ...commandBarConfig
            };
        }
        default: {
            return state;
        }
    }
}