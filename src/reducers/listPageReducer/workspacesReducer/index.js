import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import resourceWorkspacesReducer from './resourceWorkspacesReducer';
import jobWorkspacesReducer from './jobWorkspacesReducer';

export default function listPageWorkspacesReducer(state = initialState.listPage, action) {
    switch (action.type) {
        // Resource workspace actions
        case actionTypes.LOAD_RESOURCE_WORKSPACES_SUCCESSFUL:
        case actionTypes.LOAD_RESOURCE_WORKSPACE_SUCCESSFUL:
        case actionTypes.CREATE_RESOURCE_WORKSPACE_SUCCESSFUL:
        case actionTypes.DIGEST_SELECT_RESOURCE_WORKSPACE:
        case actionTypes.SELECT_RESOURCE_WORKSPACE:
        case actionTypes.ADD_MOST_RECENTLY_USED_RESOURCE_WORKSPACE:
        case actionTypes.SAVE_RESOURCE_WORKSPACE_SETTINGS:
        case actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY:
            return {
                ...state,
                resourceWorkspaces: resourceWorkspacesReducer(state.resourceWorkspaces, action)
            };

        // Job workspace actions
        case actionTypes.LOAD_JOB_WORKSPACES_SUCCESSFUL:
        case actionTypes.LOAD_JOB_WORKSPACE_SUCCESSFUL:
        case actionTypes.CREATE_JOB_WORKSPACE_SUCCESSFUL:
        case actionTypes.DIGEST_SELECT_JOB_WORKSPACE:
        case actionTypes.SELECT_JOB_WORKSPACE:
        case actionTypes.ADD_MOST_RECENTLY_USED_JOB_WORKSPACE:
        case actionTypes.SAVE_JOB_WORKSPACE_SETTINGS:
        case actionTypes.SET_JOB_WORKSPACE_MANAGE_WINDOW_VISIBILITY:
            return {
                ...state,
                jobWorkspaces: jobWorkspacesReducer(state.jobWorkspaces, action)
            };

        default:
            return state;
    }
}
