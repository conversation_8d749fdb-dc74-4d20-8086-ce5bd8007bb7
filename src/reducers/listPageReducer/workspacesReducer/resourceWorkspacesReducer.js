import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import { buildStructure, addItem } from '../../../utils/commonUtils';
import createChangesReducer from '../../commonReducers/changesReducer';
import { configureWorkspaceSettings, configureWorkspaceStructure } from '../../plannerPageReducer/workspacesReducer';

const createWsFn = (change, options) => {
    return {
        change,
        options
    };
};

const wsStructureChangesReducer = createChangesReducer(createWsFn);
const wsSettingsChangesReducer = createChangesReducer(createWsFn);

const buildWorkspacesFromResponse = (workspaces) => {
    const loadedWSStructures = workspaces[0];
    const loadFirstMostRecentlyUsedWorkspace = workspaces[1].length > 0 ? workspaces[1][0] : null;

    const allWorkspaces = loadedWSStructures.map((workspace) => configureWorkspaceStructure(workspace));
    const workspacesSettings = loadFirstMostRecentlyUsedWorkspace ? [loadFirstMostRecentlyUsedWorkspace] : [];
    const mostRecentlyUsed = workspaces[1].map(workspace => workspace.workspace_guid);

    return {
        selected: loadFirstMostRecentlyUsedWorkspace ? loadFirstMostRecentlyUsedWorkspace.workspace_guid : '',
        workspacesStructure: buildStructure("workspace_guid", allWorkspaces),
        workspacesSettings: buildStructure("workspace_guid", workspacesSettings.map(configureWorkspaceSettings)),
        mostRecentlyUsed,
        storedWorkspaceSettings: {},
        cachedMostRecentWorkspaces: workspaces[1]
    };
};

export default function resourceWorkspacesReducer(state = initialState.listPage.resourceWorkspaces, action) {
    switch (action.type) {
        case actionTypes.LOAD_RESOURCE_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const newState = buildWorkspacesFromResponse(data);

            return {
                ...state,
                ...newState
            };
        }

        case actionTypes.LOAD_RESOURCE_WORKSPACE_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const workspaceData = configureWorkspaceStructure(data[0]);

            return {
                ...state,
                workspacesStructure: !state.workspacesStructure.map || !state.workspacesStructure.map[workspaceData.workspace_guid] 
                    ? addItem(state.workspacesStructure, (workspace) => workspace, workspaceData) 
                    : state.workspacesStructure
            };
        }

        case actionTypes.CREATE_RESOURCE_WORKSPACE_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const workspaceData = configureWorkspaceStructure(data);

            return {
                ...state,
                workspacesStructure: addItem(state.workspacesStructure, (workspace) => workspace, workspaceData),
                selected: workspaceData.workspace_guid
            };
        }

        case actionTypes.DIGEST_SELECT_RESOURCE_WORKSPACE:
        case actionTypes.SELECT_RESOURCE_WORKSPACE: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                selected: workspaceGuid
            };
        }

        case actionTypes.ADD_MOST_RECENTLY_USED_RESOURCE_WORKSPACE: {
            const { payload } = action;
            const { workspaceGuid, workspaceStructure } = payload;

            const existingIndex = state.mostRecentlyUsed.indexOf(workspaceGuid);
            let newMostRecentlyUsed = [...state.mostRecentlyUsed];

            if (existingIndex !== -1) {
                newMostRecentlyUsed.splice(existingIndex, 1);
            }

            newMostRecentlyUsed.unshift(workspaceGuid);

            // Keep only the most recent 10 workspaces
            if (newMostRecentlyUsed.length > 10) {
                newMostRecentlyUsed = newMostRecentlyUsed.slice(0, 10);
            }

            return {
                ...state,
                mostRecentlyUsed: newMostRecentlyUsed,
                cachedMostRecentWorkspaces: [workspaceStructure, ...((state.cachedMostRecentWorkspaces || []).filter(ws => ws.workspace_guid !== workspaceGuid))]
            };
        }

        case actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY: {
            const { payload } = action;
            const { visible } = payload;

            return {
                ...state,
                manageMyWorkspacesWindowVisible: visible
            };
        }

        default:
            return state;
    }
}
