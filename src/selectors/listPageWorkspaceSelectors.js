import { createSelector } from 'reselect';
import { TABLE_NAMES } from '../constants/globalConsts';
import { getActiveListView } from './listPageSelectors';

// Base selectors for workspace data
const getResourceWorkspaces = createSelector(
    state => state.listPage.resourceWorkspaces,
    (resourceWorkspaces) => resourceWorkspaces
);

const getJobWorkspaces = createSelector(
    state => state.listPage.jobWorkspaces,
    (jobWorkspaces) => jobWorkspaces
);

// Current view workspace selector
const getCurrentViewWorkspaces = createSelector(
    getActiveListView,
    getResourceWorkspaces,
    getJobWorkspaces,
    (activeListView, resourceWorkspaces, jobWorkspaces) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceWorkspaces : jobWorkspaces;
    }
);

// Resource workspace selectors
const getSelectedResourceWorkspaceGuid = createSelector(
    getResourceWorkspaces,
    (resourceWorkspaces) => resourceWorkspaces.selected
);

const getResourceWorkspacesStructure = createSelector(
    getResourceWorkspaces,
    (resourceWorkspaces) => resourceWorkspaces.workspacesStructure
);

const getResourceWorkspacesSettings = createSelector(
    getResourceWorkspaces,
    (resourceWorkspaces) => resourceWorkspaces.workspacesSettings
);

const getSelectedResourceWorkspaceStructure = createSelector(
    getResourceWorkspacesStructure,
    getSelectedResourceWorkspaceGuid,
    (workspacesStructure, selectedGuid) => {
        if (!workspacesStructure.map || !selectedGuid) {
            return {};
        }
        return workspacesStructure.map[selectedGuid] || {};
    }
);

const getSelectedResourceWorkspaceSettings = createSelector(
    getResourceWorkspacesSettings,
    getSelectedResourceWorkspaceGuid,
    (workspacesSettings, selectedGuid) => {
        if (!workspacesSettings.map || !selectedGuid) {
            return {};
        }
        return workspacesSettings.map[selectedGuid] || {};
    }
);

const getResourceMostRecentlyUsedWorkspaces = createSelector(
    getResourceWorkspaces,
    (resourceWorkspaces) => resourceWorkspaces.mostRecentlyUsed || []
);

const getOrderedResourcePrivateWorkspaces = createSelector(
    getResourceWorkspacesStructure,
    (workspacesStructure) => {
        if (!workspacesStructure.map) {
            return [];
        }
        return Object.values(workspacesStructure.map)
            .filter(workspace => workspace && workspace.workspace_accesstype === 'private')
            .sort((a, b) => a.workspace_description.localeCompare(b.workspace_description));
    }
);

const getOrderedResourcePublicWorkspaces = createSelector(
    getResourceWorkspacesStructure,
    (workspacesStructure) => {
        if (!workspacesStructure.map) {
            return [];
        }
        return Object.values(workspacesStructure.map)
            .filter(workspace => workspace && workspace.workspace_accesstype === 'public')
            .sort((a, b) => a.workspace_description.localeCompare(b.workspace_description));
    }
);

// Job workspace selectors
const getSelectedJobWorkspaceGuid = createSelector(
    getJobWorkspaces,
    (jobWorkspaces) => jobWorkspaces.selected
);

const getJobWorkspacesStructure = createSelector(
    getJobWorkspaces,
    (jobWorkspaces) => jobWorkspaces.workspacesStructure
);

const getJobWorkspacesSettings = createSelector(
    getJobWorkspaces,
    (jobWorkspaces) => jobWorkspaces.workspacesSettings
);

const getSelectedJobWorkspaceStructure = createSelector(
    getJobWorkspacesStructure,
    getSelectedJobWorkspaceGuid,
    (workspacesStructure, selectedGuid) => {
        if (!workspacesStructure.map || !selectedGuid) {
            return {};
        }
        return workspacesStructure.map[selectedGuid] || {};
    }
);

const getSelectedJobWorkspaceSettings = createSelector(
    getJobWorkspacesSettings,
    getSelectedJobWorkspaceGuid,
    (workspacesSettings, selectedGuid) => {
        if (!workspacesSettings.map || !selectedGuid) {
            return {};
        }
        return workspacesSettings.map[selectedGuid] || {};
    }
);

const getJobMostRecentlyUsedWorkspaces = createSelector(
    getJobWorkspaces,
    (jobWorkspaces) => jobWorkspaces.mostRecentlyUsed || []
);

const getOrderedJobPrivateWorkspaces = createSelector(
    getJobWorkspacesStructure,
    (workspacesStructure) => {
        if (!workspacesStructure.map) {
            return [];
        }
        return Object.values(workspacesStructure.map)
            .filter(workspace => workspace && workspace.workspace_accesstype === 'private')
            .sort((a, b) => a.workspace_description.localeCompare(b.workspace_description));
    }
);

const getOrderedJobPublicWorkspaces = createSelector(
    getJobWorkspacesStructure,
    (workspacesStructure) => {
        if (!workspacesStructure.map) {
            return [];
        }
        return Object.values(workspacesStructure.map)
            .filter(workspace => workspace && workspace.workspace_accesstype === 'public')
            .sort((a, b) => a.workspace_description.localeCompare(b.workspace_description));
    }
);

// Current view selectors (dynamically switch based on active view)
const getCurrentViewSelectedWorkspaceGuid = createSelector(
    getActiveListView,
    getSelectedResourceWorkspaceGuid,
    getSelectedJobWorkspaceGuid,
    (activeListView, resourceGuid, jobGuid) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceGuid : jobGuid;
    }
);

const getCurrentViewSelectedWorkspaceStructure = createSelector(
    getActiveListView,
    getSelectedResourceWorkspaceStructure,
    getSelectedJobWorkspaceStructure,
    (activeListView, resourceStructure, jobStructure) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceStructure : jobStructure;
    }
);

const getCurrentViewSelectedWorkspaceSettings = createSelector(
    getActiveListView,
    getSelectedResourceWorkspaceSettings,
    getSelectedJobWorkspaceSettings,
    (activeListView, resourceSettings, jobSettings) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceSettings : jobSettings;
    }
);

const getCurrentViewOrderedPrivateWorkspaces = createSelector(
    getActiveListView,
    getOrderedResourcePrivateWorkspaces,
    getOrderedJobPrivateWorkspaces,
    (activeListView, resourceWorkspaces, jobWorkspaces) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceWorkspaces : jobWorkspaces;
    }
);

const getCurrentViewOrderedPublicWorkspaces = createSelector(
    getActiveListView,
    getOrderedResourcePublicWorkspaces,
    getOrderedJobPublicWorkspaces,
    (activeListView, resourceWorkspaces, jobWorkspaces) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceWorkspaces : jobWorkspaces;
    }
);

const getCurrentViewMostRecentlyUsedWorkspaces = createSelector(
    getActiveListView,
    getResourceMostRecentlyUsedWorkspaces,
    getJobMostRecentlyUsedWorkspaces,
    (activeListView, resourceWorkspaces, jobWorkspaces) => {
        return activeListView === TABLE_NAMES.RESOURCE ? resourceWorkspaces : jobWorkspaces;
    }
);

export {
    // Resource workspace selectors
    getResourceWorkspaces,
    getSelectedResourceWorkspaceGuid,
    getResourceWorkspacesStructure,
    getResourceWorkspacesSettings,
    getSelectedResourceWorkspaceStructure,
    getSelectedResourceWorkspaceSettings,
    getResourceMostRecentlyUsedWorkspaces,
    getOrderedResourcePrivateWorkspaces,
    getOrderedResourcePublicWorkspaces,
    
    // Job workspace selectors
    getJobWorkspaces,
    getSelectedJobWorkspaceGuid,
    getJobWorkspacesStructure,
    getJobWorkspacesSettings,
    getSelectedJobWorkspaceStructure,
    getSelectedJobWorkspaceSettings,
    getJobMostRecentlyUsedWorkspaces,
    getOrderedJobPrivateWorkspaces,
    getOrderedJobPublicWorkspaces,
    
    // Current view selectors
    getCurrentViewWorkspaces,
    getCurrentViewSelectedWorkspaceGuid,
    getCurrentViewSelectedWorkspaceStructure,
    getCurrentViewSelectedWorkspaceSettings,
    getCurrentViewOrderedPrivateWorkspaces,
    getCurrentViewOrderedPublicWorkspaces,
    getCurrentViewMostRecentlyUsedWorkspaces
};
