import { COMMAND_BAR_MENUS_SECTION_KEYS } from '../../constants';
import { MENU_ITEM, DIVIDER } from '../../constants/commandBarConsts';
import * as actionTypes from '../../actions/actionTypes';
import { TABLE_NAMES } from '../../constants/globalConsts';

const getWorkspacesSectionSaveChangesButton = ({ saveChangesToPublicLabel }) => {
    return {
        label: saveChangesToPublicLabel,
        labelTemplate: 'saveChangesToPublicLabel',
        type: MENU_ITEM,
        onClickActionType: '', // Will be set based on view type
        showEllipsis: true
    };
};

const getWorkspacesSectionSaveAsNewWorkspaceButton = ({ saveAsNewWorkspaceLabel }) => {
    return {
        label: saveAsNewWorkspaceLabel,
        labelTemplate: 'saveAsNewWorkspaceLabel',
        type: MENU_ITEM,
        onClickActionType: '', // Will be set based on view type
        showEllipsis: true
    };
};

const getWorkspacesSectionMenuItem = ({ labelTemplate, label, onClickActionType, workspaceGuid }) => {
    return {
        label,
        labelTemplate,
        type: MENU_ITEM,
        onClickActionType,
        plansSectionPlanGuid: workspaceGuid,
        workspaceGuid,
        showEllipsis: true
    };
};

export const createListPageWorkspacesSectionModel = (
    privateWorkspaces,
    publicWorkspaces,
    mostRecentWorkspaces,
    workspacesMessages,
    activeListView,
    selectedWorkspaceAccessType = 'private'
) => {
    const {
        defaultWorkspaceLabel,
        newWorkspaceLabel,
        saveChangesToPublicLabel,
        saveAsNewWorkspaceLabel,
        manageMyWorkspacesLabel,
        privateWorkspacesLabel,
        publicWorkspacesLabel,
        noPublicWorkspacesCreatedLabel,
        noPrivateWorkspacesCreatedLabel,
    } = workspacesMessages;

    const isResourceView = activeListView === TABLE_NAMES.RESOURCE;
    
    // Set action types based on view
    const createWorkspaceActionType = isResourceView 
        ? actionTypes.DIGEST_CREATE_RESOURCE_WORKSPACE 
        : actionTypes.DIGEST_CREATE_JOB_WORKSPACE;
    
    const saveWorkspaceActionType = isResourceView 
        ? actionTypes.SAVE_RESOURCE_WORKSPACE_SETTINGS 
        : actionTypes.SAVE_JOB_WORKSPACE_SETTINGS;
    
    const saveAsNewWorkspaceActionType = isResourceView 
        ? actionTypes.DIGEST_SAVE_AS_NEW_RESOURCE_WORKSPACE 
        : actionTypes.DIGEST_SAVE_AS_NEW_JOB_WORKSPACE;
    
    const manageWorkspacesActionType = isResourceView 
        ? actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY 
        : actionTypes.SET_JOB_WORKSPACE_MANAGE_WINDOW_VISIBILITY;
    
    const selectWorkspaceActionType = isResourceView 
        ? actionTypes.SELECT_RESOURCE_WORKSPACE 
        : actionTypes.SELECT_JOB_WORKSPACE;

    // Build workspace menu items
    const privatePlans = privateWorkspaces.map(workspace => 
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            workspaceGuid: workspace.workspace_guid
        })
    );

    const publicPlans = publicWorkspaces.map(workspace => 
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            workspaceGuid: workspace.workspace_guid
        })
    );

    const mostRecentPlans = mostRecentWorkspaces.slice(0, 5).map(workspace => 
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            workspaceGuid: workspace.workspace_guid
        })
    );

    // Add empty state messages if no workspaces exist
    if (publicPlans.length === 0) {
        publicPlans.push(getWorkspacesSectionMenuItem({ 
            labelTemplate: 'noPublicWorkspacesCreatedLabel', 
            label: noPublicWorkspacesCreatedLabel 
        }));
    }
    if (privatePlans.length === 0) {
        privatePlans.push(getWorkspacesSectionMenuItem({ 
            labelTemplate: 'noPrivateWorkspacesCreatedLabel', 
            label: noPrivateWorkspacesCreatedLabel 
        }));
    }

    const saveChangesButton = getWorkspacesSectionSaveChangesButton({ saveChangesToPublicLabel });
    saveChangesButton.onClickActionType = saveWorkspaceActionType;

    const saveAsNewButton = getWorkspacesSectionSaveAsNewWorkspaceButton({ saveAsNewWorkspaceLabel });
    saveAsNewButton.onClickActionType = saveAsNewWorkspaceActionType;

    return {
        label: defaultWorkspaceLabel,
        type: 'Menu',
        closedIcon: 'down',
        openIcon: 'up',
        manageMyPlansWindowVisible: false,
        icon: 'layout',
        triggerSubMenuAction: 'click',
        items: [
            {
                label: newWorkspaceLabel,
                labelTemplate: 'newWorkspaceLabel',
                type: MENU_ITEM,
                onClickActionType: createWorkspaceActionType,
                icon: {
                    type: 'plus',
                    position: 'left'
                }
            },
            { type: DIVIDER },
            saveChangesButton,
            saveAsNewButton,
            { type: DIVIDER },
            {
                label: manageMyWorkspacesLabel,
                labelTemplate: 'manageMyWorkspacesLabel',
                type: MENU_ITEM,
                onClickActionType: manageWorkspacesActionType,
                showEllipsis: true
            },
            { type: DIVIDER },
            ...mostRecentPlans,
            { type: DIVIDER },
            {
                label: privateWorkspacesLabel,
                labelTemplate: 'privateWorkspacesLabel',
                type: 'SubMenu',
                items: privatePlans,
                scroll: true
            },
            {
                label: publicWorkspacesLabel,
                labelTemplate: 'publicWorkspacesLabel',
                type: 'SubMenu',
                items: publicPlans,
                scroll: true
            }
        ],
        workspacesMessages
    };
};

export const createListPageWorkspacesSectionReducer = (initialWorkspacesSection) => {
    return function listPageWorkspacesSectionReducer(state = initialWorkspacesSection, action) {
        switch (action.type) {
            case actionTypes.COMMAND_BAR_WORKSPACES_SECTION.POPULATE: {
                const { payload } = action;
                const { workspaces, activeListView } = payload;
                const workspacesMessages = state.workspacesMessages;

                // Get workspaces based on active view
                const privateWorkspaces = [];
                const publicWorkspaces = [];
                const mostRecentWorkspaces = [];

                return {
                    ...state,
                    ...createListPageWorkspacesSectionModel(
                        privateWorkspaces,
                        publicWorkspaces,
                        mostRecentWorkspaces,
                        workspacesMessages,
                        activeListView
                    )
                };
            }
            default:
                return state;
        }
    };
};
