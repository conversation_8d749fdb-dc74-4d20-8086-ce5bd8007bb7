import { COMMAND_BAR_WORKSPACES_SECTION, SET_CONFIG } from "../../actions/actionTypes";
import * as actionTypes from "../../actions/actionTypes";
import { COMMAND_BAR_MENUS_COMPONENT_TYPES, COMMAND_BAR_MENUS_SECTION_KEYS } from "../../constants/commandBarConsts";
import { getMenusSectionIndexByKey } from "../../utils/commandBarUtils";
import { TABLE_NAMES } from "../../constants/globalConsts";
import initialState from "../initialState";

const { DIVIDER, MENU_ITEM } = COMMAND_BAR_MENUS_COMPONENT_TYPES;

const getMenuItemLabel = (labelTemplate, label) => {
    return labelTemplate ? {labelTemplate, label} : {label};
};

export const getWorkspacesSectionMenuItem = (menuItemProps) => {
    const { labelTemplate, label, onClickActionType = null, plansSectionPlanGuid, style = {}, icon = null, className = "" } = menuItemProps;

    return {
        ...getMenuItemLabel(labelTemplate, label),
        type: MENU_ITEM,
        onClickActionType,
        plansSectionPlanGuid,
        style,
        icon,
        className
    }
}

export const getWorkspacesSectionSaveChangesButton = (labels) => {
    let button = {};
    
    button = {
        label: labels.saveChangesToPublicLabel,
        labelTemplate: 'saveChangesToPublicLabel',
        type: MENU_ITEM,
        onClickActionType: '' // to be replaced with the action for saving changes to public
    };

    return button;
}

export const getPlansSectionSaveAsNewWorkspaceButton = (labels) => {
    let button = {};

    button = {
        label: labels.saveAsNewWorkspaceLabel,
        labelTemplate: 'saveAsNewWorkspaceLabel',
        type: MENU_ITEM,
        onClickActionType: '' // to be replaced with the action for saving as new workspace
    };

    return button;
};

export function workspacesSectionModelCreator (publicWorkspaces = [], privateWorkspaces = [], messages, activeListView, mostRecentWorkspaces = []) {
    const { defaultWorkspaceLabel, newWorkspaceLabel, saveChangesToPublicLabel, saveAsNewWorkspaceLabel, manageMyWorkspacesLabel, privateWorkspacesLabel, publicWorkspacesLabel, noPublicWorkspacesCreatedLabel, noPrivateWorkspacesCreatedLabel } = messages;

    const isResourceView = activeListView === TABLE_NAMES.RESOURCE;

    // Set action types based on view
    const createWorkspaceActionType = isResourceView
        ? actionTypes.DIGEST_CREATE_RESOURCE_WORKSPACE
        : actionTypes.DIGEST_CREATE_JOB_WORKSPACE;

    const saveWorkspaceActionType = isResourceView
        ? actionTypes.SAVE_RESOURCE_WORKSPACE_SETTINGS
        : actionTypes.SAVE_JOB_WORKSPACE_SETTINGS;

    const saveAsNewWorkspaceActionType = isResourceView
        ? actionTypes.DIGEST_SAVE_AS_NEW_RESOURCE_WORKSPACE
        : actionTypes.DIGEST_SAVE_AS_NEW_JOB_WORKSPACE;

    const manageWorkspacesActionType = isResourceView
        ? actionTypes.SET_RESOURCE_WORKSPACE_MANAGE_WINDOW_VISIBILITY
        : actionTypes.SET_JOB_WORKSPACE_MANAGE_WINDOW_VISIBILITY;

    const selectWorkspaceActionType = isResourceView
        ? actionTypes.SELECT_RESOURCE_WORKSPACE
        : actionTypes.SELECT_JOB_WORKSPACE;

    // Build workspace menu items
    const privatePlans = privateWorkspaces.map(workspace =>
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            plansSectionPlanGuid: workspace.workspace_guid
        })
    );

    const publicPlans = publicWorkspaces.map(workspace =>
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            plansSectionPlanGuid: workspace.workspace_guid
        })
    );

    const mostRecentPlans = mostRecentWorkspaces.slice(0, 5).map(workspace =>
        getWorkspacesSectionMenuItem({
            labelTemplate: 'default',
            label: workspace.workspace_description,
            onClickActionType: selectWorkspaceActionType,
            plansSectionPlanGuid: workspace.workspace_guid
        })
    );

    if(publicPlans.length === 0){
        publicPlans.push(getWorkspacesSectionMenuItem({ labelTemplate: 'noPublicWorkspacesCreatedLabel', label: noPublicWorkspacesCreatedLabel }));
    }
    if(privatePlans.length === 0){
        privatePlans.push(getWorkspacesSectionMenuItem({ labelTemplate: 'noPrivateWorkspacesCreatedLabel', label: noPrivateWorkspacesCreatedLabel }));
    }

    const saveChangesButton = getWorkspacesSectionSaveChangesButton({saveChangesToPublicLabel});
    saveChangesButton.onClickActionType = saveWorkspaceActionType;

    const saveAsNewButton = getPlansSectionSaveAsNewWorkspaceButton({ saveAsNewWorkspaceLabel });
    saveAsNewButton.onClickActionType = saveAsNewWorkspaceActionType;

    return {
        label: defaultWorkspaceLabel,
        type: 'Menu',
        closedIcon: "down",
        openIcon: "up",
        manageMyPlansWindowVisible: false,
        icon: 'layout',
        triggerSubMenuAction: "click",
        items: [
            {
                label: newWorkspaceLabel,
                labelTemplate: 'newWorkspaceLabel',
                type: MENU_ITEM,
                onClickActionType: createWorkspaceActionType,
                icon: {
                    type: 'plus',
                    position: 'left'
                }
            },
            { type: DIVIDER },
            saveChangesButton,
            saveAsNewButton,
            { type: DIVIDER },
            {
                label: manageMyWorkspacesLabel,
                labelTemplate: 'manageMyWorkspacesLabel',
                type: MENU_ITEM,
                onClickActionType: manageWorkspacesActionType,
                showEllipsis: true
            },
            { type: DIVIDER },
            ...mostRecentPlans,
            { type: DIVIDER },
            {
                label: privateWorkspacesLabel,
                labelTemplate: 'privateWorkspacesLabel',
                type: 'SubMenu',
                items: privatePlans,
                scroll: true
            },
            {
                label: publicWorkspacesLabel,
                labelTemplate: 'publicWorkspacesLabel',
                type: 'SubMenu',
                items: publicPlans,
                scroll: true
            }
        ]
    };
};

const plansSectionIndex = getMenusSectionIndexByKey(initialState.jobsPage.commandBarConfig.menusSection, COMMAND_BAR_MENUS_SECTION_KEYS.PLANS);
export default function workspacesSectionReducer(state = initialState.jobsPage.commandBarConfig.menusSection[plansSectionIndex], action) {
    switch (action.type) {
        case COMMAND_BAR_WORKSPACES_SECTION.POPULATE: {
            const { payload } = action;
            const { privateWorkspaces = [], publicWorkspaces = [], mostRecentWorkspaces = [], activeListView } = payload;
            const workspacesMessages = state.workspacesMessages;

            return {
                ...state,
                ...workspacesSectionModelCreator(publicWorkspaces, privateWorkspaces, workspacesMessages, activeListView, mostRecentWorkspaces)
            };
        }
        case SET_CONFIG: {
            const { config } = action.payload;

            return {
                ...state,
                ...config.menusSection[plansSectionIndex]
            };
        }
        default:
            return state;
    }
}