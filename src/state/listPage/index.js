import createChangesCollection from '../../utils/changesCollectionUtils';

export default {
    activeListView: 'job',
    // Separate workspace states for Resource and Job views
    resourceWorkspaces: {
        selected: '',
        workspacesStructure: { map: {} },
        workspacesStructureChanges: createChangesCollection("id", [], [], []),
        workspacesSettings: {},
        workspacesSettingsChanges: createChangesCollection("id", [], [], []),
        workspacesSettingsChangesTracker: createChangesCollection("id", [], [], []),
        mostRecentlyUsed: [],
        storedWorkspaceSettings: {}
    },
    jobWorkspaces: {
        selected: '',
        workspacesStructure: { map: {} },
        workspacesStructureChanges: createChangesCollection("id", [], [], []),
        workspacesSettings: {},
        workspacesSettingsChanges: createChangesCollection("id", [], [], []),
        workspacesSettingsChangesTracker: createChangesCollection("id", [], [], []),
        mostRecentlyUsed: [],
        storedWorkspaceSettings: {}
    }
}