import createChangesCollection from '../../utils/changesCollectionUtils';

export default {
    activeListView: 'job',
    // Separate workspace states for Resource and Job views
    resourceWorkspaces: {
        selected: '',
        workspacesStructure: {},
        workspacesStructureChanges: createChangesCollection("id", [], [], []),
        workspacesSettings: {},
        workspacesSettingsChanges: createChangesCollection("id", [], [], []),
        workspacesSettingsChangesTracker: createChangesCollection("id", [], [], []),
        mostRecentlyUsed: [],
        storedWorkspaceSettings: {}
    },
    jobWorkspaces: {
        selected: '',
        workspacesStructure: {},
        workspacesStructureChanges: createChangesCollection("id", [], [], []),
        workspacesSettings: {},
        workspacesSettingsChanges: createChangesCollection("id", [], [], []),
        workspacesSettingsChangesTracker: createChangesCollection("id", [], [], []),
        mostRecentlyUsed: [],
        storedWorkspaceSettings: {}
    }
}